# Generated by Django 5.2.4 on 2025-07-15 06:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0003_notice_content_notice_is_important_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='SchoolInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='Noagaon High School', max_length=200)),
                ('tagline', models.Char<PERSON>ield(default='Excellence in Education Since 1950', max_length=300)),
                ('logo', models.ImageField(blank=True, help_text='School logo', null=True, upload_to='logo/')),
                ('address', models.TextField(blank=True)),
                ('phone', models.CharField(blank=True, max_length=50)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('website', models.URLField(blank=True)),
                ('established_year', models.PositiveIntegerField(default=1950)),
                ('about', models.TextField(blank=True, help_text='About the school')),
                ('is_active', models.<PERSON>oleanField(default=True, help_text='Use this school info')),
            ],
            options={
                'verbose_name': 'School Information',
                'verbose_name_plural': 'School Information',
                'ordering': ['-is_active'],
            },
        ),
    ]
