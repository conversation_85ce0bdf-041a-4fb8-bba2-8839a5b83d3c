# Generated by Django 5.2.4 on 2025-07-15 06:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0002_headmaster'),
    ]

    operations = [
        migrations.AddField(
            model_name='notice',
            name='content',
            field=models.TextField(blank=True, help_text='Write notice content here (only for text type notices)'),
        ),
        migrations.AddField(
            model_name='notice',
            name='is_important',
            field=models.BooleanField(default=False, help_text='Mark as important notice'),
        ),
        migrations.AddField(
            model_name='notice',
            name='notice_type',
            field=models.CharField(choices=[('pdf', 'PDF Upload'), ('text', 'Text Content')], default='pdf', help_text='Choose whether to upload a PDF or write text content', max_length=10),
        ),
        migrations.AddField(
            model_name='notice',
            name='summary',
            field=models.Char<PERSON>ield(blank=True, help_text='Brief summary for text notices (optional)', max_length=300),
        ),
        migrations.Alter<PERSON>ield(
            model_name='notice',
            name='pdf_file',
            field=models.FileField(blank=True, help_text='Upload PDF file (only for PDF type notices)', null=True, upload_to='notices/'),
        ),
    ]
