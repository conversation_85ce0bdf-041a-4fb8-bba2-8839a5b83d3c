# Generated by Django 5.2.4 on 2025-07-16 06:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0005_gallerycategory_galleryimage'),
    ]

    operations = [
        migrations.CreateModel(
            name='NavigationLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(help_text='Display name for the link', max_length=100)),
                ('title_bn', models.Char<PERSON>ield(blank=True, help_text='Bengali title (optional)', max_length=100)),
                ('link_type', models.CharField(choices=[('internal', 'Internal Page'), ('external', 'External URL'), ('file', 'File Download')], default='external', max_length=10)),
                ('url', models.URLField(blank=True, help_text='External URL (for external links)')),
                ('internal_page', models.Char<PERSON><PERSON>(blank=True, help_text="Internal page name (e.g., 'main:home', 'main:notices')", max_length=100)),
                ('file_upload', models.FileField(blank=True, help_text='File to download (for file links)', null=True, upload_to='navigation_files/')),
                ('position', models.CharField(choices=[('main', 'Main Navigation'), ('important', 'Important Links'), ('footer', 'Footer Links')], default='important', max_length=20)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('is_active', models.BooleanField(default=True)),
                ('open_new_tab', models.BooleanField(default=False, help_text='Open link in new tab')),
                ('icon_class', models.CharField(blank=True, help_text="CSS icon class (e.g., 'fas fa-home')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Link description')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Navigation Link',
                'verbose_name_plural': 'Navigation Links',
                'ordering': ['position', 'order', 'title'],
            },
        ),
    ]
