<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}School Website{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e40af',
                        secondary: '#64748b',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="{% url 'main:home' %}" class="flex items-center">
                        <div class="flex-shrink-0 flex items-center justify-between">
                            {% if school_logo %}
                                <img src="{{ school_logo }}" alt="{{ school_name }}" class="h-12 w-12 mr-3 rounded-full object-cover">
                            {% endif %}
                            <div>
                                <h1 class="text-xl font-bold text-primary">{{ school_name }}</h1>
                                <p class="text-xs text-gray-600 hidden sm:block">{{ school_tagline }}</p>
                            </div>
                        </div>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="{% url 'main:home' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Home</a>
                    <a href="{% url 'main:notices' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Notices</a>
                    <a href="{% url 'main:teachers' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Teachers</a>
                    <a href="{% url 'main:headmaster' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Headmaster</a>
                    <a href="{% url 'main:gallery' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Gallery</a>
                    <a href="{% url 'main:committee' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Managing Committee</a>
                    <a href="{% url 'main:contact' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">Contact</a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button type="button" class="mobile-menu-button text-gray-700 hover:text-primary focus:outline-none focus:text-primary">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-50">
                <a href="{% url 'main:home' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Home</a>
                <a href="{% url 'main:notices' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Notices</a>
                <a href="{% url 'main:teachers' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Teachers</a>
                <a href="{% url 'main:headmaster' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Headmaster</a>
                <a href="{% url 'main:gallery' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Gallery</a>
                <a href="{% url 'main:committee' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Managing Committee</a>
                <a href="{% url 'main:contact' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">Contact</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="flex-1">
        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12 text-center">
        <div class="border-t border-gray-700 mt-8 pt-6 text-center">
            <p class="text-gray-400 text-sm text-center">© 2024 {{ school_name }}. All rights reserved.</p>
            <p class="text-gray-400 text-sm text-center">This website is Developed by Md Arifur Rahman Anik</p>
        </div>
    </footer>

    <!-- Mobile menu toggle script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuButton = document.querySelector('.mobile-menu-button');
            const mobileMenu = document.querySelector('.mobile-menu');

            mobileMenuButton.addEventListener('click', function() {
                mobileMenu.classList.toggle('hidden');
            });
        });
    </script>
</body>
</html>
