{% extends 'base.html' %}

{% block title %}{{ notice.title }} - School Website{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-blue-600 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-4">
            <a href="{% url 'main:notices' %}" class="text-blue-200 hover:text-white mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl md:text-4xl font-bold">Notice Details</h1>
        </div>
        <p class="mt-2 text-blue-100">{{ notice.title }}</p>
    </div>
</div>

<!-- Notice Content -->
<div class="py-12 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8 {% if notice.is_important %}border-l-4 border-red-500{% endif %}">
            <!-- Notice Header -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl md:text-3xl font-bold text-gray-900">{{ notice.title }}</h1>
                        {% if notice.is_important %}
                            <span class="ml-3 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                Important
                            </span>
                        {% endif %}
                    </div>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        {{ notice.get_notice_type_display }}
                    </span>
                </div>
                
                <div class="flex items-center text-sm text-gray-500 mb-6">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <span>Published on {{ notice.upload_date|date:"F d, Y" }}</span>
                    <span class="mx-2">•</span>
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>By {{ notice.created_by.get_full_name|default:notice.created_by.username }}</span>
                </div>
            </div>
            
            <!-- Notice Content -->
            {% if notice.notice_type == 'text' %}
                {% if notice.summary %}
                    <div class="mb-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                        <h3 class="text-lg font-semibold text-blue-900 mb-2">Summary</h3>
                        <p class="text-blue-800">{{ notice.summary }}</p>
                    </div>
                {% endif %}
                
                <div class="prose max-w-none">
                    <div class="text-gray-700 leading-relaxed text-lg">{{ notice.content|linebreaks }}</div>
                </div>
            {% else %}
                <!-- PDF Notice -->
                <div class="text-center py-8">
                    <div class="mb-6">
                        <svg class="mx-auto h-16 w-16 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">PDF Notice</h3>
                    <p class="text-gray-600 mb-6">This notice is available as a PDF document. Click the button below to download and view the complete notice.</p>
                    <a href="{{ notice.pdf_file.url }}" target="_blank" 
                       class="inline-flex items-center px-6 py-3 bg-blue-600 text-white text-lg font-medium rounded-md hover:bg-blue-700 transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Download PDF Notice
                    </a>
                </div>
            {% endif %}
        </div>
        
        <!-- Back to Notices Button -->
        <div class="mt-8 text-center">
            <a href="{% url 'main:notices' %}" 
               class="inline-flex items-center px-6 py-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700 transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                Back to All Notices
            </a>
        </div>
    </div>
</div>
{% endblock %}
