{% extends 'base.html' %}

{% block title %}Headmaster - School Website{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-indigo-600 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <h1 class="text-3xl md:text-4xl font-bold">Our Headmaster</h1>
        <p class="mt-2 text-indigo-100">Meet the visionary leader guiding our educational journey</p>
    </div>
</div>

{% if headmaster %}
<!-- Headmaster Profile -->
<div class="py-12 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="md:flex">
                <!-- Photo Section -->
                <div class="md:w-1/3">
                    {% if headmaster.photo %}
                        <img src="{{ headmaster.photo.url }}" alt="{{ headmaster.name }}" 
                             class="w-full h-96 md:h-full object-cover">
                    {% else %}
                        <div class="w-full h-96 md:h-full bg-gray-200 flex items-center justify-center">
                            <svg class="w-24 h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Information Section -->
                <div class="md:w-2/3 p-8">
                    <div class="mb-6">
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">{{ headmaster.name }}</h2>
                        <p class="text-xl text-indigo-600 font-semibold mb-4">Headmaster</p>
                        
                        {% if headmaster.qualification %}
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                                </svg>
                                <span class="text-gray-700"><strong>Qualification:</strong> {{ headmaster.qualification }}</span>
                            </div>
                        {% endif %}
                        
                        {% if headmaster.experience_years %}
                            <div class="flex items-center mb-3">
                                <svg class="w-5 h-5 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-gray-700"><strong>Experience:</strong> {{ headmaster.experience_years }} years</span>
                            </div>
                        {% endif %}
                        
                        {% if headmaster.contact_info %}
                            <div class="flex items-center mb-6">
                                <svg class="w-5 h-5 text-gray-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-gray-700"><strong>Contact:</strong> {{ headmaster.contact_info }}</span>
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if headmaster.bio %}
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">About</h3>
                            <p class="text-gray-700 leading-relaxed">{{ headmaster.bio|linebreaks }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Message from Headmaster -->
{% if headmaster.message %}
<div class="py-12 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-indigo-50 rounded-lg p-8">
            <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-indigo-900">Message from the Headmaster</h3>
            </div>
            <div class="relative">
                <svg class="absolute top-0 left-0 w-8 h-8 text-indigo-300" fill="currentColor" viewBox="0 0 32 32">
                    <path d="M9.352 4C4.456 4 .496 8.048.496 13.056c0 5.056 3.552 9.248 8.256 9.248 1.216 0 2.352-.256 3.36-.736l3.648 3.648a.8.8 0 001.152 0l1.152-1.152a.8.8 0 000-1.152L14.4 19.264c.48-1.008.736-2.144.736-3.36C15.136 8.048 11.088 4 9.352 4z"/>
                </svg>
                <div class="pl-12">
                    <p class="text-gray-700 text-lg leading-relaxed italic">{{ headmaster.message|linebreaks }}</p>
                </div>
                <div class="text-right mt-4">
                    <p class="text-indigo-600 font-semibold">- {{ headmaster.name }}</p>
                    <p class="text-gray-500 text-sm">Headmaster</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Additional Information Sections -->
<div class="py-12 bg-gray-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid gap-8 md:grid-cols-2">
            <!-- Education Background -->
            {% if headmaster.education %}
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    Educational Background
                </h3>
                <div class="text-gray-700">{{ headmaster.education|linebreaks }}</div>
            </div>
            {% endif %}
            
            <!-- Achievements -->
            {% if headmaster.achievements %}
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <svg class="w-6 h-6 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                    </svg>
                    Achievements & Awards
                </h3>
                <div class="text-gray-700">{{ headmaster.achievements|linebreaks }}</div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% else %}
<!-- No Headmaster Information -->
<div class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <svg class="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
        </svg>
        <h3 class="text-xl font-medium text-gray-900 mb-2">Headmaster Information Not Available</h3>
        <p class="text-gray-500">The headmaster's profile information will be available soon.</p>
    </div>
</div>
{% endif %}
{% endblock %}
