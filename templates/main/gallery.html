{% extends 'base.html' %}

{% block title %}Gallery - {{ school_name }}{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-teal-600 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <h1 class="text-3xl md:text-4xl font-bold">School Gallery</h1>
        <p class="mt-2 text-teal-100">Explore our school campus and activities through images</p>
    </div>
</div>

<!-- Category Filter -->
<div class="bg-white shadow-sm border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center overflow-x-auto py-4 whitespace-nowrap">
            <a href="{% url 'main:gallery' %}" 
               class="px-4 py-2 rounded-full text-sm font-medium mr-2 {% if not selected_category %}bg-teal-600 text-white{% else %}bg-gray-100 text-gray-800 hover:bg-gray-200{% endif %} transition-colors">
                All Images
            </a>
            
            {% for category in categories %}
                <a href="{% url 'main:gallery' %}?category={{ category.id }}" 
                   class="px-4 py-2 rounded-full text-sm font-medium mr-2 {% if selected_category.id == category.id %}bg-teal-600 text-white{% else %}bg-gray-100 text-gray-800 hover:bg-gray-200{% endif %} transition-colors">
                    {{ category.name }}
                </a>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Gallery Grid -->
<div class="py-12 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if selected_category %}
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">{{ selected_category.name }}</h2>
                {% if selected_category.description %}
                    <p class="text-gray-600">{{ selected_category.description }}</p>
                {% endif %}
            </div>
        {% endif %}
        
        {% if page_obj %}
            <div class="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {% for image in page_obj %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <a href="{% url 'main:gallery_detail' image.id %}" class="block">
                        <div class="aspect-w-4 aspect-h-3">
                            <img src="{{ image.image.url }}" alt="{{ image.title }}" 
                                 class="w-full h-48 object-cover">
                        </div>
                        <div class="p-4">
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ image.title }}</h3>
                            <p class="text-sm text-teal-600">{{ image.category.name }}</p>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <div class="mt-12">
                <nav class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6 rounded-lg shadow">
                    <div class="flex flex-1 justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?{% if selected_category %}category={{ selected_category.id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                               class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?{% if selected_category %}category={{ selected_category.id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                               class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    
                    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing
                                <span class="font-medium">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium">{{ page_obj.paginator.count }}</span>
                                images
                            </p>
                        </div>
                        <div>
                            <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?{% if selected_category %}category={{ selected_category.id }}&{% endif %}page={{ page_obj.previous_page_number }}" 
                                       class="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                        <span class="sr-only">Previous</span>
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                                
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <span class="relative z-10 inline-flex items-center bg-teal-600 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-teal-600">{{ num }}</span>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <a href="?{% if selected_category %}category={{ selected_category.id }}&{% endif %}page={{ num }}" 
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% endfor %}
                                
                                {% if page_obj.has_next %}
                                    <a href="?{% if selected_category %}category={{ selected_category.id }}&{% endif %}page={{ page_obj.next_page_number }}" 
                                       class="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">
                                        <span class="sr-only">Next</span>
                                        <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </nav>
            </div>
            {% endif %}
        {% else %}
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No images</h3>
                <p class="mt-1 text-sm text-gray-500">No images have been uploaded to this gallery yet.</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
