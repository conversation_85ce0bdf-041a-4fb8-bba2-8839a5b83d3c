{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Home" %} - {{ school_name }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
    <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-4">Welcome to {{ school_name }}</h1>
            <p class="text-xl md:text-2xl mb-8 text-blue-100">{{ school_tagline }}</p>
            <div class="flex justify-center">
                {% if school_logo %}
                    <div class="w-32 h-32 bg-white rounded-full flex items-center justify-center p-2">
                        <img src="{{ school_logo }}" alt="{{ school_name }}" class="w-full h-full object-cover rounded-full">
                    </div>
                {% else %}
                    <div class="w-32 h-32 bg-white rounded-full flex items-center justify-center">
                        <span class="text-blue-600 text-4xl font-bold">LOGO</span>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- About Section -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900 mb-8">{% trans "About" %} {{ school_name }}</h2>
            <p class="text-lg text-gray-600 max-w-3xl mx-auto">
                {% blocktrans with school_name=school_name %}
                {{ school_name }} has been a cornerstone of educational excellence in the community for over 70 years.
                We are committed to providing quality education and nurturing young minds to become responsible
                citizens and future leaders. Our dedicated faculty and modern facilities ensure that every student
                receives the best possible education in a supportive and inspiring environment.
                {% endblocktrans %}
            </p>
        </div>
    </div>
</div>

<!-- Latest Notices Section -->
<div class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">{% trans "Latest Notices" %}</h2>
            <p class="text-gray-600">{% trans "Stay updated with our latest announcements" %}</p>
        </div>
        
        {% if latest_notices %}
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {% for notice in latest_notices %}
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow {% if notice.is_important %}border-l-4 border-red-500{% endif %}">
                    <div class="flex items-center mb-2">
                        <h3 class="text-lg font-semibold text-gray-900">{{ notice.title }}</h3>
                        {% if notice.is_important %}
                            <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Important
                            </span>
                        {% endif %}
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <span>{{ notice.upload_date|date:"F d, Y" }}</span>
                        <span class="mx-2">•</span>
                        <span class="px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">{{ notice.get_notice_type_display }}</span>
                    </div>

                    {% if notice.notice_type == 'text' %}
                        {% if notice.summary %}
                            <p class="text-gray-600 text-sm mb-4">{{ notice.summary|truncatewords:15 }}</p>
                        {% else %}
                            <p class="text-gray-600 text-sm mb-4">{{ notice.content|truncatewords:15 }}</p>
                        {% endif %}
                        <a href="{% url 'main:notice_detail' notice.id %}"
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            Read More
                        </a>
                    {% else %}
                        <a href="{{ notice.pdf_file.url }}" target="_blank"
                           class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Download PDF
                        </a>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            
            <div class="text-center mt-8">
                <a href="{% url 'main:notices' %}" 
                   class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 transition-colors">
                    View All Notices
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        {% else %}
            <div class="text-center py-8">
                <p class="text-gray-500">No notices available at the moment.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Quick Links Section -->
<div class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">{% trans "Quick Links" %}</h2>
        </div>
        
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
            <a href="{% url 'main:notices' %}" class="group">
                <div class="bg-blue-50 rounded-lg p-6 text-center hover:bg-blue-100 transition-colors">
                    <div class="w-12 h-12 bg-blue-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600">Notices</h3>
                </div>
            </a>

            <a href="{% url 'main:teachers' %}" class="group">
                <div class="bg-green-50 rounded-lg p-6 text-center hover:bg-green-100 transition-colors">
                    <div class="w-12 h-12 bg-green-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-green-600">Teachers</h3>
                </div>
            </a>

            <a href="{% url 'main:headmaster' %}" class="group">
                <div class="bg-indigo-50 rounded-lg p-6 text-center hover:bg-indigo-100 transition-colors">
                    <div class="w-12 h-12 bg-indigo-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-indigo-600">Headmaster</h3>
                </div>
            </a>

            <a href="{% url 'main:gallery' %}" class="group">
                <div class="bg-teal-50 rounded-lg p-6 text-center hover:bg-teal-100 transition-colors">
                    <div class="w-12 h-12 bg-teal-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-teal-600">Gallery</h3>
                </div>
            </a>

            <a href="{% url 'main:committee' %}" class="group">
                <div class="bg-purple-50 rounded-lg p-6 text-center hover:bg-purple-100 transition-colors">
                    <div class="w-12 h-12 bg-purple-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-purple-600">Committee</h3>
                </div>
            </a>

            <a href="{% url 'main:contact' %}" class="group">
                <div class="bg-orange-50 rounded-lg p-6 text-center hover:bg-orange-100 transition-colors">
                    <div class="w-12 h-12 bg-orange-600 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-orange-600">Contact</h3>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}
